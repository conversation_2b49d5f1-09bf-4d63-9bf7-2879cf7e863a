package com.allcore.external.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 30 7月 2025
 */
@Data
@ApiModel("用户信息")
public class UserVO implements Serializable {

    private static final long serialVersionUID = -4349360639518315542L;

    @ApiModelProperty("用户id")
    private String id;

    @ApiModelProperty("昵称")
    private String name;

    @ApiModelProperty("用户账号")
    private String account;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty("用户真实姓名")
    private String realName;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
