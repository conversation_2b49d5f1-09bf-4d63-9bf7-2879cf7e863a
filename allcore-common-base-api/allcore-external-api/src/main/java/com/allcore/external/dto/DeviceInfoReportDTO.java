package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 30 7月 2025
 */
@Data
@ApiModel("设备信息上报实体")
public class DeviceInfoReportDTO implements Serializable {

    private static final long serialVersionUID = 8898130974316681857L;

    @ApiModelProperty(value = "设备id", required = true)
    private String deviceId;

    @ApiModelProperty(value = "设备名称",required = true)
    private String deviceName;

    @ApiModelProperty(value = "用户id",required = true)
    private String userId;

    @ApiModelProperty(value = "经度",required = true)
    private String lat;

    @ApiModelProperty(value = "纬度",required = true)
    private String lng;
}
