# SGC WebSocket 消息发送功能实现说明

## 概述

根据您的需求，我已经在 `SgcAlarmMessagingEndpoint` 中添加了发送WebSocket消息的功能，用于主动查询特定机器人的告警信息。

## 实现的功能

### 1. 发送消息功能
- 在 `SgcAlarmMessagingEndpoint` 中添加了 `queryAlarmInfo(String agvIp)` 方法
- 支持发送格式为 `{"agvIp":"************"}` 的消息体
- 包含连接状态检查和错误处理

### 2. REST API接口
- 在 `SgcController` 中添加了 `/sgc/queryAlarmInfo` 接口
- 支持通过HTTP POST请求触发WebSocket消息发送
- 提供友好的响应消息

### 3. 配置支持
- 在 `SgcProperties` 中添加了 `wsAlarmUrl` 配置项
- 支持通过配置文件自定义WebSocket服务地址

## 修改的文件

### 1. SgcAlarmMessagingEndpoint.java
```java
// 添加了以下功能：
- 注入 WebSocketClientManager 依赖
- 修改 getWebSocketUrl() 方法支持配置
- 添加 queryAlarmInfo(String agvIp) 方法
- 添加 isConnected() 连接状态检查方法
```

### 2. SgcController.java
```java
// 添加了以下功能：
- 注入 SgcAlarmMessagingEndpoint 依赖
- 添加 /sgc/queryAlarmInfo 接口
- 支持通过REST API调用WebSocket发送功能
```

### 3. SgcProperties.java
```java
// 添加了配置项：
- wsAlarmUrl: WebSocket告警服务地址
```

## 使用方法

### 1. 通过REST API调用
```bash
POST /sgc/queryAlarmInfo?agvIp=************
```

### 2. 配置WebSocket地址（可选）
在配置文件中添加：
```yaml
sgc:
  wsAlarmUrl: ws://your-websocket-server:port/path
```

### 3. 直接调用方法
```java
@Autowired
private SgcAlarmMessagingEndpoint sgcAlarmMessagingEndpoint;

// 发送查询请求
boolean success = sgcAlarmMessagingEndpoint.queryAlarmInfo("************");
```

## WebSocket连接机制

### ⚠️ **重要说明**
WebSocket连接有两种建立方式：

#### 方式1: 前端订阅触发（推荐）
```javascript
// 前端通过WebSocket连接，并在请求头中指定business-type
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);
stompClient.connect({
    'business-type': 'sgc'  // 关键：指定业务类型
}, function(frame) {
    // 连接成功后，后端会自动建立与第三方的WebSocket连接
    stompClient.subscribe('/topic/sgc', function(message) {
        // 接收告警信息
        console.log('收到告警信息:', JSON.parse(message.body));
    });
});
```

#### 方式2: REST接口自动建立
调用 `/sgc/queryAlarmInfo` 接口时，系统会自动检查并建立WebSocket连接。

## 消息流程

1. **建立连接**: 前端订阅或REST接口自动建立WebSocket连接
2. **发送请求**: 调用 `/sgc/queryAlarmInfo` 接口或直接调用 `queryAlarmInfo()` 方法
3. **构造消息**: 系统自动构造 `{"agvIp":"指定IP"}` 格式的JSON消息
4. **发送消息**: 通过WebSocket连接发送到第三方服务
5. **接收响应**: 第三方服务返回对应机器人的告警信息
6. **处理响应**: 现有的 `WebSocketMessageHandler` 会处理返回的告警数据

## 注意事项

1. **连接建立**: 系统会自动确保WebSocket连接已建立
2. **参数验证**: 会验证 `agvIp` 参数不能为空
3. **错误处理**: 包含完整的错误处理和日志记录
4. **配置灵活**: 支持通过配置文件自定义WebSocket地址
5. **自动重连**: 如果连接不存在，系统会自动建立连接

## 测试建议

1. 确保WebSocket连接已建立
2. 使用有效的机器人IP地址进行测试
3. 检查日志输出确认消息发送状态
4. 验证第三方服务是否正确返回告警信息

## 扩展性

该实现参考了 `AgvMissionRecordEndpoint` 的设计模式，具有良好的扩展性：
- 可以轻松添加更多消息类型
- 支持不同的消息格式
- 便于维护和调试
