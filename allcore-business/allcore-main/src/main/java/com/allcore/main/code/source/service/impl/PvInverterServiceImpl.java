package com.allcore.main.code.source.service.impl;

import cn.afterturn.easypoi.entity.vo.TemplateExcelConstants;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.view.PoiBaseView;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;

import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;


import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.common.vo.PVTreeNode;
import com.allcore.main.code.source.dto.PvInverterDTO;

import com.allcore.main.code.source.dto.PvStringDTO;
import com.allcore.main.code.source.entity.*;


import com.allcore.main.code.source.excel.PvInverterExcel;
import com.allcore.main.code.source.mapper.PvAreaMapper;
import com.allcore.main.code.source.mapper.PvInverterMapper;
import com.allcore.main.code.source.mapper.PvStringMapper;
import com.allcore.main.code.source.service.IDeviceQrCodeService;
import com.allcore.main.code.source.service.IPvInverterService;
import com.allcore.main.code.source.service.IPvInverterStringService;
import com.allcore.main.code.source.service.IPvStringService;
import com.allcore.main.code.source.vo.PvInverterVO;
import com.allcore.main.code.source.vo.PvStringVO;
import com.allcore.main.code.source.wrapper.PvStringWrapper;
import com.allcore.main.utils.PoiUtils;
import com.allcore.main.utils.PvInverterImportVerifyHandler;
import com.allcore.system.cache.SysCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;

import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: bl
 * @description: 光伏逆变器服务实现类
 * @author: fanxiang
 * @create: 2025-05-22 15:37
 **/

@Service
@AllArgsConstructor
public class PvInverterServiceImpl extends ZxhcServiceImpl<PvInverterMapper, PvInverter> implements IPvInverterService {


    private final IPvInverterStringService pvInverterStringService;

    private final PvStringMapper pvStringMapper;

    private final IDeviceQrCodeService deviceQrCodeService;
    private final PvInverterImportVerifyHandler pvInverterImportVerifyHandler;

    private final PvAreaMapper pvAreaMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R savePvInverter(PvInverterDTO pvInverterDTO) {
        // 输入参数校验
        if (pvInverterDTO == null) {
            throw new ServiceException("输入的逆变器数据为空");
        }

        // DTO 转换为实体类
        PvInverter entity = BeanUtil.copyProperties(pvInverterDTO, PvInverter.class);
        if (entity == null) {
            throw new ServiceException("实体类转换失败");
        }

        // 检查设备名称是否重复
        this.checkRepeatName(entity.getDeviceName(), entity.getDeptCode(), entity.getId());

        // 检查光伏区域是否合法
        this.checkPvArea(entity.getInverterNumber(),entity.getPvAreaId());

        // 保存逆变器实体
        boolean saveResult = this.save(entity);
        if (!saveResult) {
            throw new ServiceException("逆变器实体保存失败");
        }

        if(Func.isNotEmpty(pvInverterDTO.getPvStringIds())){
            // 校验光伏组串是否在所选的光伏区域下
            this.checkPvString(pvInverterDTO.getPvStringIds(),entity.getPvAreaId());
        }

        // 处理逆变器与组件的关联数据
        List<PvInverterString> entityList = new ArrayList<>();
        if (Func.isNotEmpty(pvInverterDTO.getPvStringIds())) {

            for (String pvStringId : pvInverterDTO.getPvStringIds()) {
                PvInverterString pvInverterString = new PvInverterString();
                pvInverterString.setInverterId(entity.getId());
                pvInverterString.setPvStringId(pvStringId);
                entityList.add(pvInverterString);
            }
            // 批量保存关联数据
            if (!entityList.isEmpty()) {
                boolean batchSaveResult = pvInverterStringService.saveBatch(entityList);
                if (!batchSaveResult) {
                    throw new ServiceException("逆变器组件关联数据保存失败");
                }
            }
        }

        return R.success("逆变器相关数据保存成功！");
    }

    private void checkPvString(List<String> pvStringIds, String pvAreaId) {

        // 查询光伏组串信息
        List<PvString> pvStrings = pvStringMapper.selectList(new LambdaQueryWrapper<PvString>()
                .in(PvString::getId, pvStringIds));
        if (pvStrings.size() != pvStringIds.size()) {
            throw new ServiceException("光伏组串不存在");
        }

        if(!pvStrings.stream().allMatch(pvString -> pvString.getPvAreaId().equals(pvAreaId))){
            throw new ServiceException("光伏组串不在所选的光伏区域下");
        }

    }

    private void checkPvArea(String no,String pvAreaId) {

        if (StringUtil.isBlank(pvAreaId)) {
            throw new ServiceException("光伏区域不能为空");
        }

        // 查询光伏区域信息
        PvArea pvArea = pvAreaMapper.selectById(pvAreaId);
        if (pvArea == null) {
            throw new ServiceException("光伏区域不存在");
        }

        int lastIndex = no.lastIndexOf("-");
        if(lastIndex!=-1){
            String areaNo=no.substring(0,lastIndex);
            if(!areaNo.equals(pvArea.getAreaNumber())){
                throw new ServiceException("所选光伏区域不合法");
            }
        }


    }

    @Override
    public IPage<PvInverterVO> selectPvInverterPage(PvInverterDTO pvInverterDTO, IPage<PvInverterVO> page) {
        if (Func.isBlank(pvInverterDTO.getDeptCode())) {
            pvInverterDTO.setDeptCode(AuthUtil.getDeptCode());
        }

        Map<String, List<String>> result = pvInverterStringService.list(new LambdaQueryWrapper<PvInverterString>()
                        .select(PvInverterString::getInverterId, PvInverterString::getPvStringId))
                .stream()
                .collect(Collectors.groupingBy(
                        PvInverterString::getInverterId, // 直接使用 inverterId
                        Collectors.mapping(
                                PvInverterString::getPvStringId, // 直接使用 pvStringId
                                Collectors.toList()
                        )
                ));

        List<PvInverter> pvInverters = baseMapper.selectPvInverterPage(page, pvInverterDTO);
        List<PvInverterVO> collect = pvInverters.stream().map(x -> {
            PvInverterVO pvInverterVO = BeanUtil.copyProperties(x, PvInverterVO.class);
            if (Func.isNotBlank(x.getDeptCode())) {
                String deptName = SysCache.getDeptNameByDeptCode(x.getDeptCode());
                pvInverterVO.setDeptName(StringUtil.isBlank(deptName) ? "" : deptName);
            }
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                pvInverterVO.setUsageTime(sdf.parse(x.getUsageTime()));
            } catch (Exception e) {
                // 建议添加日志记录
                log.error("时间转换异常", e);
            }

            if (result.containsKey(x.getId())) {
                pvInverterVO.setPvStringIds(result.get(x.getId()));
            }
            return pvInverterVO;
        }).collect(Collectors.toList());
        return page.setRecords(collect);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updatePvInverter(PvInverterDTO pvInverterDTO) {
        PvInverter entity = BeanUtil.copyProperties(pvInverterDTO, PvInverter.class);

        //同级不能有同名设备
        this.checkRepeatName(entity.getDeviceName(), entity.getDeptCode(), entity.getId());
        // 检查光伏区域是否合法
        this.checkPvArea(entity.getInverterNumber(),entity.getPvAreaId());

        if (Func.isNotEmpty(pvInverterDTO.getPvStringIds())) {
            // 校验光伏组串是否在所选的光伏区域下
            this.checkPvString(pvInverterDTO.getPvStringIds(),entity.getPvAreaId());
        }

        // 更新逆变器实体
        boolean updateInverter = this.updateById(entity);
        if (!updateInverter) {
            throw new ServiceException("逆变器实体更新失败");
        }

        //删除关联关系
        boolean removeResult = pvInverterStringService.remove(new LambdaQueryWrapper<PvInverterString>().eq(PvInverterString::getInverterId, entity.getId()));
        if (!removeResult) {
            throw new ServiceException("删除逆变器组件关联关系失败");
        }

        //保存关联关系
        if (Func.isNotEmpty(pvInverterDTO.getPvStringIds())) {
            List<PvInverterString> entityList = new ArrayList<>();
            for (String pvStringId : pvInverterDTO.getPvStringIds()) {
                PvInverterString pvInverterString = new PvInverterString();
                pvInverterString.setInverterId(entity.getId());
                pvInverterString.setPvStringId(pvStringId);
                entityList.add(pvInverterString);
            }
            // 批量保存关联数据
            if (!entityList.isEmpty()) {
                boolean saveBatch = pvInverterStringService.saveBatch(entityList);
                if (!saveBatch) {
                    throw new ServiceException("逆变器组件关联数据保存失败");
                }
            }
        }

        return R.success("逆变器相关数据更新成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deletePvInverter(List<String> ids) {
        // 参数校验
        if (ids == null || ids.isEmpty()) {
            return R.fail("删除失败：ID列表为空");
        }

        try {
            // 删除逆变器组件关联表相关数据
            boolean removeStringSuccess = pvInverterStringService.remove(
                    new LambdaQueryWrapper<PvInverterString>().in(PvInverterString::getInverterId, ids)
            );

            // 删除逆变器表相关数据
            boolean removeInverterSuccess = this.remove(
                    new LambdaQueryWrapper<PvInverter>().in(PvInverter::getId, ids)
            );

            // 删除二维码相关信息

            List<String> fileGuids = deviceQrCodeService.list(Wrappers.<DeviceQrCode>lambdaQuery().in(DeviceQrCode::getDeviceId, ids)).stream()
                    .map(DeviceQrCode::getQrCodeFileGuid)
                    .filter(Func::isNotBlank)
                    .collect(Collectors.toList());
            boolean qrcodeRemove = true;
            if (Func.isNotEmpty(fileGuids)) {
                deviceQrCodeService.cleanupOldFile(fileGuids);
                qrcodeRemove = deviceQrCodeService.remove(Wrappers.<DeviceQrCode>lambdaQuery().in(DeviceQrCode::getDeviceId, ids));
            }

            // 根据删除结果返回状态
            if (removeStringSuccess && removeInverterSuccess && qrcodeRemove) {
                return R.success("删除成功");
            } else {
                return R.fail("删除失败：部分数据未成功删除");
            }
        } catch (Exception e) {
            log.error("删除逆变器失败，原因: {}", e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    @Override
    public R<List<String>> getAllModel() {
        try {
            // 查询逆变器型号数据
            List<PvInverter> pvInverterList = this.list(new LambdaQueryWrapper<PvInverter>()
                    .select(PvInverter::getInverterModel)
                    .groupBy(PvInverter::getInverterModel));

            // 防止查询结果为null
            if (pvInverterList == null) {
                return R.data(new ArrayList<>());
            }

            List<String> inverterModels = pvInverterList.stream()
                    .filter(Objects::nonNull) // 过滤null对象
                    .map(PvInverter::getInverterModel)
                    .filter(Func::isNotBlank) // 过滤空白字符串
                    .distinct()
                    .collect(Collectors.toList());

            return R.data(inverterModels);
        } catch (Exception e) {
            log.error("获取逆变器型号数据失败", e);
            return R.fail("获取逆变器型号数据失败: " + e.getMessage());
        }
    }

    @Override
    public R<PvInverterVO> getInverterDetail(PvInverterDTO dto) {
        // 1. 校验输入 ID
        if (dto == null || dto.getId() == null) {
            return R.fail("设备 ID 不能为空");
        }

        // 2. 查询逆变器信息
        PvInverter pvInverter = baseMapper.selectById(dto.getId());
        if (pvInverter == null) {
            return R.fail("所查询的设备不存在");
        }

        // 3. 转换为 VO 并设置部门名称
        PvInverterVO vo = BeanUtil.copyProperties(pvInverter, PvInverterVO.class);
        if (Func.isNotBlank(pvInverter.getDeptCode())) {
            String deptNameByDeptCode = SysCache.getDeptNameByDeptCode(pvInverter.getDeptCode());
            vo.setDeptName(StringUtil.isBlank(deptNameByDeptCode) ? "" : deptNameByDeptCode);
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            vo.setUsageTime(sdf.parse(pvInverter.getUsageTime()));
        } catch (Exception e) {
            log.error("时间转换异常", e);
        }

        // 4. 查询光伏字符串（避免分页限制）
        List<PvStringVO> pvStringVOs = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(dto.getPvStringIds())) {
            PvStringDTO pvStringDTO = new PvStringDTO();
            pvStringDTO.setPvStringIds(dto.getPvStringIds());
            pvStringVOs = pvStringMapper.selectPvstringVoList(pvStringDTO);
        }

        // 5. 包装光伏字符串列表并返回
        vo.setPvStringList(PvStringWrapper.build().listVoByVo(pvStringVOs));
        return R.data(vo);
    }

    private void checkRepeatName(String name, String deptCode, String id) {
        Optional.ofNullable(super.getOne(new LambdaQueryWrapper<PvInverter>()
                .eq(PvInverter::getDeviceName, name)
                .eq(PvInverter::getDeptCode, deptCode)
                .ne(Objects.nonNull(id), PvInverter::getId, id))).ifPresent(f -> {
            throw new ServiceException("设备名称重复");
        });
    }

    @Override
    public List<PVTreeNode> selectPvInverterTree(String deptCode) {
        return baseMapper.selectPvInverterTree(deptCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importExcel(MultipartFile file) throws Exception {
        val inverterExcels = PoiUtils.getExcelList(file, PvInverterExcel.class, pvInverterImportVerifyHandler);

        String deptCode = AuthUtil.getDeptCode();
        List<PvString> pvStrings = pvStringMapper.selectList(Wrappers.<PvString>lambdaQuery().likeRight(PvString::getDeptCode, deptCode));
        List<PvArea> pvAreas = pvAreaMapper.selectList(Wrappers.<PvArea>lambdaQuery().likeRight(PvArea::getDeptCode, deptCode));
        Map<String, String> stringMap = pvAreas.stream().collect(Collectors.toMap(PvArea::getAreaNumber, PvArea::getId));

        List<PvInverterString> pvInverterStrings = new ArrayList<>();
        List<PvInverter> list = inverterExcels.stream().map(inverterExcel -> {
            PvInverter inverter = new PvInverter();
            String pvAreaId=null;
            if(stringMap.containsKey(inverterExcel.getAttachedPvArea())){

                pvAreaId=stringMap.get(inverterExcel.getAttachedPvArea());

                inverter.setPvAreaId(pvAreaId);
            }else{
                throw new ServiceException("没有该光伏区数据");
            }
            BeanUtils.copyProperties(inverterExcel, inverter);
            for (PvString pvString : pvStrings) {
                if (pvString.getStringNumber().equals(inverterExcel.getAttachedString())) {
                    // 验证该组串是否在光伏区域下
                    if (!pvString.getPvAreaId().equals(pvAreaId)) {
                        throw new ServiceException("逆变器关联的光伏组串不在光伏区域下");
                    }
                    inverter.setLongitude(pvString.getLongitude());
                    inverter.setLatitude(pvString.getLatitude());
                    break;
                }
            }
            return inverter;
        }).collect(Collectors.toList());


        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            throw new ServiceException("表没有有效数据，新增失败");
        }
        if (list.size() != list.stream().map(PvInverter::getDeviceName).distinct().count()) {
            throw new ServiceException("表中有重复的逆变器名称，请检查!");
        }
        boolean b = super.saveBatch(list);
        if (b) {
            for (PvInverter pvInverter : list) {
                for (PvString pvString : pvStrings) {
                    if (pvString.getStringNumber().equals(pvInverter.getAttachedString())) {
                        PvInverterString pvInverterString = new PvInverterString();
                        pvInverterString.setInverterId(pvInverter.getId());
                        pvInverterString.setPvStringId(pvString.getId());
                        pvInverterStrings.add(pvInverterString);
                    }
                }
            }
        }
        if (Func.isEmpty(pvInverterStrings)) {
            throw new ServiceException("表中逆变器关联的光伏组串不存在，请检查!");
        }
        boolean b1 = pvInverterStringService.saveBatch(pvInverterStrings);
        if (!b || !b1) {
            throw new ServiceException("导入逆变器数据失败");
        }
        return R.data("导入逆变器数据共：" + inverterExcels.size() + "条" + ",成功导入:" + list.size());
    }

    @Override
    public void downTemplate(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response) {
        String filePath = CommonConstant.INVERTER_TEMPLATE_PATH;
        String fileName = CommonConstant.INVERTER_TEMPLATE_NAME;

        if (StringUtil.isNotBlank(filePath)) {
            Map<String, Object> map = Maps.newHashMap();
            // true:导出全部sheet false:只导出第一个sheet
            TemplateExportParams params = new TemplateExportParams(filePath, true);
            modelMap.put(TemplateExcelConstants.FILE_NAME, fileName);
            modelMap.put(TemplateExcelConstants.PARAMS, params);
            modelMap.put(TemplateExcelConstants.MAP_DATA, map);
            PoiBaseView.render(modelMap, request, response, TemplateExcelConstants.EASYPOI_TEMPLATE_EXCEL_VIEW);
        }
    }

    @Override
    public List<BasicCommonVO> listCommon(DeviceCommonDTO dto) {
        if (StringUtil.isBlank(dto.getDeptCode())) {
            dto.setDeptCode(AuthUtil.getDeptCode());
        }
        List<BasicCommonVO> returnList = new ArrayList<>();
        List<PvInverter> list = this.list(Wrappers.<PvInverter>lambdaQuery()
                .likeRight(StringUtil.isNotBlank(dto.getDeptCode()), PvInverter::getDeptCode, dto.getDeptCode())
                .isNotNull(dto.isCoordinate(), PvInverter::getLongitude).isNotNull(dto.isCoordinate(), PvInverter::getLatitude)
                .in(CollectionUtils.isNotEmpty(dto.getDeviceIds()), PvInverter::getId, dto.getDeviceIds())
                .orderByAsc(PvInverter::getInverterNumber));
        List<String> ids = list.stream().map(PvInverter::getId).collect(Collectors.toList());
        // 获取关联组串
        Map<String, List<PvInverterString>> collect = pvInverterStringService.list(new LambdaQueryWrapper<PvInverterString>().in(PvInverterString::getInverterId, ids))
                .stream().collect(Collectors.groupingBy(PvInverterString::getInverterId));

        list.forEach(e -> {
            returnList.add(new BasicCommonVO().setDeviceId(e.getId()).setDeviceName(e.getInverterNumber())
                    .setDeviceType(dto.getDeviceType()).setDeviceModel(e.getInverterModel())
                    .setRatePower(e.getRatedPower())
                    .setPvStringNumber(collect.get(e.getId()) != null ? collect.get(e.getId()).size() : 0)
                    .setLongitude(e.getLongitude()).setLatitude(e.getLatitude())
                    .setRelatedName(extractName(e.getInverterNumber())));
        });
        return returnList;
    }


    private String extractName(String input) {
        String[] split = input.split("-");
        if (split.length != 3) {
            return "";
        }
        return "东站-" + Integer.parseInt(split[1]) + "#光伏区" + Integer.parseInt(split[2]) + "#逆变器";
    }
}
