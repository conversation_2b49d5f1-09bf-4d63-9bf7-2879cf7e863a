package com.allcore.external.controller;

import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.external.dto.DeviceInfoReportDTO;

import com.allcore.external.vo.UserVO;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 30 7月 2025
 */
@RestController
@RequestMapping("/wear/device")
@RequiredArgsConstructor
@Api(value = "穿戴设备", tags = "穿戴设备")
public class WearDeviceController {

    private final IUserClient userClient;

    @ApiOperation("获取用户列表")
    @GetMapping("/user/list")
    public R<List<UserVO>> getUserList() {
        List<User> users = userClient.selectUserList(1, 0, "yes");
        List<UserVO> voList = BeanUtil.copy(users, UserVO.class);
        return R.data(voList);
    }

    @ApiOperation("设备信息上报")
    @PostMapping("/info/report")
    public R infoReport(@RequestBody DeviceInfoReportDTO dto) {
        return R.status(true);
    }
}
