package com.allcore.external.service;

import com.allcore.core.mp.support.Query;
import com.allcore.external.dto.AlarmInfoQueryDTO;
import com.allcore.external.dto.AlarmInfoUpdateDTO;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.entity.PositionDevice;
import com.allcore.external.vo.AlarmInfoVO;
import com.allcore.external.vo.PositionWithPeopleVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 09 5月 2025
 */
public interface IAlarmInfoService extends IService<AlarmInfo> {
    /**
     * 分页查询
     *
     * @param dto
     * @param query
     * @return
     */
    IPage<AlarmInfoVO> getPageList(AlarmInfoQueryDTO dto, Query query);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    AlarmInfoVO getDetail(String id);

    /**
     * 修改告警信息
     *
     * @param dto
     * @return
     */
    Boolean update(List<AlarmInfoUpdateDTO> dto);

    /**
     * 根据任务id查询告警信息
     *
     * @param inspectionTaskId
     * @return
     */
    AlarmInfo getOneByInspectionTaskId(String inspectionTaskId);


    AlarmInfo getAlarmInfoByTaggingId(String inspectionPictureTaggingId);

    List<AlarmInfoVO> getList(AlarmInfoQueryDTO dto);

    void fenceSaveAlarmInfo(PositionWithPeopleVO vo);

    boolean dealWith(String alarmId);
}
