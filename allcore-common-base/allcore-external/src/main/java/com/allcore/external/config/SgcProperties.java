package com.allcore.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-05-07 09:21
 **/

@Data
@Component
@ConfigurationProperties(prefix = "sgc")
public class SgcProperties {

    /**
     * 服务地址
     */
    private String host;

    /**
     * token
     */
    private String token;

    /**
     *  rws视频流地址ws
     */
    private String rwsUrl;

    /**
     * 双目云台rtsp 可见光推流地址
     */
    private String visUrl;

    /**
     *  双目云台rtsp 红外推流地址
     */
    private String infUrl;

    /**
     * WebSocket告警服务地址
     */
    private String wsAlarmUrl;
}
