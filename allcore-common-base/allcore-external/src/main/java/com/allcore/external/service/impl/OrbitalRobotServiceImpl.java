package com.allcore.external.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.config.TetraProperties;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.dto.tetra.TetraMediaReqDTO;
import com.allcore.external.dto.tetra.TetraRequestDTO;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.entity.OribitialRobotConfig;
import com.allcore.external.enums.OrbitalAlarmLevelEnum;
import com.allcore.external.enums.OrbitalAlarmTypeEnum;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.service.OribitalRobotConfigService;
import com.allcore.external.vo.tetra.*;
import com.allcore.external.service.IOrbitialRobotService;
import com.allcore.external.utils.TetraClientUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.allcore.external.constant.ExternalConstant.Tetra.*;

@Service
@Slf4j
public class OrbitalRobotServiceImpl implements IOrbitialRobotService {
	final
	private  TetraProperties tetraProperties;

	final
	private MongoTemplate template;
	final
	private AllcoreRedis allcoreRedis;
	private final IAlarmInfoService alarmInfoService;

	private final OribitalRobotConfigService orbitalRobotConfigService;
	private  final Map<String, String> MEDIA_URLS;
    public OrbitalRobotServiceImpl(TetraProperties tetraProperties, MongoTemplate template, AllcoreRedis allcoreRedis, IAlarmInfoService alarmInfoService, OribitalRobotConfigService orbitalRobotConfigService) {
        this.tetraProperties = tetraProperties;
        this.template = template;
        this.allcoreRedis = allcoreRedis;
        this.alarmInfoService = alarmInfoService;
        this.orbitalRobotConfigService = orbitalRobotConfigService;

        MEDIA_URLS = new HashMap<>();
		MEDIA_URLS.put(BRAND_DH+"_"+MEDIA_TYPE_VISUAL, tetraProperties.getDhVisualUrl());
		MEDIA_URLS.put(BRAND_DH+"_"+MEDIA_TYPE_INFRARED, tetraProperties.getDhInfraredUrl());
		MEDIA_URLS.put(BRAND_HK+"_"+MEDIA_TYPE_VISUAL, tetraProperties.getHkVisualUrl());
		MEDIA_URLS.put(BRAND_HK+"_"+MEDIA_TYPE_INFRARED, tetraProperties.getHkInfraredUrl());
		MEDIA_URLS.put(BRAND_JG+"_"+MEDIA_TYPE_INFRARED, tetraProperties.getJgInfraredUrl());
    }
	@Override
	public R<List<RobotInfoVO>> getRobotList() {
		return null;
	}

	@Override
	public R<RobotDetailVO> getRobotDetail(TetraRequestDTO dto) {
		String url = "http://" +dto.getHost()+ ExternalConstant.Tetra.ROBOT_DETAIL;
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("robotId", dto.getRobotId());
		try {
			JSONObject fetchedObj = this.fetchObj(url, paramMap, dto.getHost());
			if (fetchedObj != null) {
				return R.data(JSONObject.parseObject(fetchedObj.toJSONString(), RobotDetailVO.class));
			}
		}catch (Exception e){
			log.error("获取轨道机器人详情异常");
			e.printStackTrace();
		}
		//todo 数据存储
		return R.fail("获取轨道机器人详情失败");
	}

	@Override
	public R<RobotMapInfoVO> getRobotMapInfo(TetraRequestDTO dto) {
		String url = "http://" +dto.getHost()+ ExternalConstant.Tetra.ROBOT_MAP_INFO;
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("robotId", dto.getRobotId());
		try {
			JSONObject fetchedObj = this.fetchObj(url, paramMap,dto.getHost());
			if (fetchedObj != null) {
				return R.data(JSONObject.parseObject(fetchedObj.toJSONString(), RobotMapInfoVO.class));
			}
		}catch (Exception e){
			log.error("获取地图异常");
			e.printStackTrace();
		}
		return R.fail("获取地图失败");
	}

	@Override
	public R<RobotMapDetailVO> getRobotMapDetail(TetraRequestDTO dto) {
		String url = "http://" +dto.getHost()+ ExternalConstant.Tetra.ROBOT_MAP_DETAIL;
		try {
			JSONObject fetchedObj = this.fetchObj(url, new HashMap<>(), dto.getHost());
			if (fetchedObj != null) {
				return R.data(JSONObject.parseObject(fetchedObj.toJSONString(), RobotMapDetailVO.class));
			}
		}catch (Exception e){
			log.error("获取机器人地图数据异常");
			e.printStackTrace();
		}
		//todo 数据存储
		return R.fail("获取机器人地图数据失败");
	}

	@Override
	public R<RobotCurrentTaskVO> getCurrentTaskInfo(TetraRequestDTO dto) {
		String url = "http://" +dto.getHost()+ ExternalConstant.Tetra.CURRENT_TASK_INFO;
		try {
			JSONObject fetchedObj = this.fetchObj(url, new HashMap<>(),dto.getHost());
            String s = null;
            if (fetchedObj != null) {
                s = JSON.parseObject(fetchedObj.toJSONString(), String.class);
            }
            if(StringUtils.isNotBlank(s) && "当前未执行任务".equals(s)){
				return R.success("0");
			}
			if (fetchedObj != null) {
				RobotCurrentTaskVO robotCurrentTaskVO = JSONObject.parseObject(fetchedObj.toJSONString(), RobotCurrentTaskVO.class);
				if(robotCurrentTaskVO!=null){
					robotCurrentTaskVO.setBeginTime("2025-06-04 09:00:00");
					robotCurrentTaskVO.setDuringTime(formatDuration(robotCurrentTaskVO.getBeginTime()));
				}
				return R.data(robotCurrentTaskVO);
			}

		}catch (Exception e){
			log.error("获取机器人当前巡检任务数据异常");
			e.printStackTrace();
		}
		//todo 数据存储
		return R.fail("获取机器人当前巡检任务数据失败");
	}

	@Override
	public R<String> getRtspMedia(TetraMediaReqDTO dto) {
		String key = dto.getBrand()+"_"+dto.getType();
		if (StringUtils.isBlank(MEDIA_URLS.get(key))){
			throw new ServiceException("暂不支持该品牌");
		}
		return R.data(MEDIA_URLS.get(key));
	}

	/**
	 * 第三方返回json数据处理逻辑，包括401错误码重试机制
	 * @param url
	 * @param paramMap
	 * @return
	 */
	private JSONObject fetchObj(String url,Map<String, Object> paramMap,String host)  {
		String token = this.getToken2Redis(host);
		Map<String, Object> headersMap = new HashMap<>();
		headersMap.put("Authorization", token);

		JSONObject resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);
		log.info("获取数据结果：{}",resultObj);

		if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
//			return resultObj.getJSONObject("data");
			if(resultObj.containsKey("data")){
				return resultObj.getJSONObject("data");
			}else {
				String msg = resultObj.getString("msg");
				JSONObject msgObj = new JSONObject();
				msgObj.put("message", msg);
				return msgObj;
			}

		} else if (resultObj != null && "401".equalsIgnoreCase(resultObj.getString("code"))) {
			// Token失效，重新获取Token并重试
			allcoreRedis.del(ExternalConstant.TETRA_TOKEN+host);
			String newToken = this.getToken2Redis(host);
			headersMap.put("Authorization", newToken);
			resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);

			if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
				return resultObj.getJSONObject("data");
			}
		}
		return null;
	}
	private String getToken2Redis(String host) {
		String token = null;
		try {
			//先从redis中获取token，获取不到在调用第三方接口返回token
			String redisToken = allcoreRedis.get(ExternalConstant.TETRA_TOKEN+host);
			if (StringUtils.isNotBlank(redisToken)) {
				return redisToken;
			}
			R<String> tokenR = this.getToken(host);
			if (tokenR.isSuccess()) {
				token = tokenR.getData();
			}
		} catch (Exception e) {
			log.error("获取token异常:{}", e);
			throw new ServiceException("获取轨道机器人token异常");
		}
		return token;
	}

	@Override
	public R<String> getToken(String host)  {
		String token = null;
		try {
			String url = "http://" +host + ExternalConstant.Tetra.TOKEN;
			OribitialRobotConfig configServiceOne = orbitalRobotConfigService.getOne(new LambdaQueryWrapper<OribitialRobotConfig>().eq(OribitialRobotConfig::getHost, host));
			if (configServiceOne == null){
				return R.data(token);
			}

			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("username", configServiceOne.getUsername());
			paramMap.put("password", configServiceOne.getPassword());

			token = TetraClientUtil.getToken(url, paramMap);

			if (StringUtils.isNotBlank(token)) {
				allcoreRedis.setEx(ExternalConstant.TETRA_TOKEN+host, token, Duration.ofDays(30));
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		return R.data(token);
	}

	@Override
	public void saveAlarmData(OrbitalRobotAlarmVO data) {
		if(data !=null && data.getData() !=null ){
			String deptCode = "";
			OribitialRobotConfig configServiceOne = orbitalRobotConfigService.getOne(new LambdaQueryWrapper<OribitialRobotConfig>().eq(OribitialRobotConfig::getRobotId, data.getData().getRobotId()));
			if (configServiceOne != null){
				deptCode = configServiceOne.getDeptCode();
			}

			AlarmInfo alarmInfo = AlarmInfo.builder()
					.deptId(deptCode)

					.deviceId(data.getData().getAlarmTypeName())
					.deviceType("BOOSTER")
					.alarmSource("oribital_robot")
					.alarmType(OrbitalAlarmTypeEnum.getValue(data.getData().getAlarmType()))
					.alarmContent(data.getData().getAlarmContent())
					.alarmLevel(OrbitalAlarmLevelEnum.getValue(data.getData().getAlarmLevel()) == null ? "general" : OrbitalAlarmLevelEnum.getValue(data.getData().getAlarmLevel()))
					.alarmStatus(0)
					.alarmTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.parse(data.getData().getStartTime(), "yyyy-MM-dd HH:mm:ss")))
					.createTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.now()))
					.updateTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.now()))
					.build();
			alarmInfoService.save(alarmInfo);
			template.save(data);
		}
	}

	@Override
	public void saveAlarmData4Task(RealTaskResultVO.VpApparatusRecordListDTO vpApparatusRecord,Integer robotId) {
		if (vpApparatusRecord != null) {
			String deptCode;
			OribitialRobotConfig configServiceOne = orbitalRobotConfigService.getOne(new LambdaQueryWrapper<OribitialRobotConfig>().eq(OribitialRobotConfig::getRobotId, robotId));
			if (configServiceOne != null) {
				deptCode = configServiceOne.getDeptCode();
			} else {
				deptCode = "";
			}

			AlarmInfo alarmInfo = AlarmInfo.builder()
					.deptId(deptCode)
					//
					.deviceId(vpApparatusRecord.getDeviceName())
					.deviceType("BOOSTER")
					.alarmSource("oribital_robot")
					.alarmType("real_time_type")
					.alarmContent(vpApparatusRecord.getAlarmContent())
					.alarmLevel(OrbitalAlarmLevelEnum.getValue(vpApparatusRecord.getAlarmLevel()) == null ? "general" : OrbitalAlarmLevelEnum.getValue(vpApparatusRecord.getAlarmLevel()))
					.alarmStatus(0)
					.alarmTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.parse(vpApparatusRecord.getCreateTime(), "yyyy-MM-dd HH:mm:ss")))
					.createTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.now()))
					.updateTime(com.allcore.core.tool.utils.DateUtil.fromDate(com.allcore.core.tool.utils.DateUtil.now()))
					.build();
			alarmInfoService.save(alarmInfo);
			RealTaskResultVO realTaskResultVO = new RealTaskResultVO();
			realTaskResultVO.setRobotId(robotId);
			realTaskResultVO.setVpApparatusRecordList(Arrays.asList(vpApparatusRecord));
			template.save(realTaskResultVO);
		}
	}

	private  String formatDuration(String beginTime) {
		if (StringUtil.isNotBlank(beginTime)) {
			try {
				// 定义时间格式
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				// 解析开始时间字符串为 LocalDateTime 对象
				LocalDateTime startTime = LocalDateTime.parse(beginTime, formatter);
				// 获取当前时间
				LocalDateTime now = LocalDateTime.now();
				// 计算时间差
				Duration duration = Duration.between(startTime, now);
				// 获取总秒数
				long totalSeconds = duration.getSeconds();

				// 计算小时、分钟和秒
				long hours = totalSeconds / 3600;
				long minutes = (totalSeconds % 3600) / 60;
				long seconds = totalSeconds % 60;

				// 格式化为 HH:mm:ss
				return String.format("%02d:%02d:%02d", hours, minutes, seconds);
			} catch (Exception e) {
				log.error("时间转换异常", e);
			}
		}
		return "";
	}


}
