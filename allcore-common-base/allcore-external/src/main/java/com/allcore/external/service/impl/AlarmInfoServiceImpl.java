package com.allcore.external.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.allcore.common.constant.BasicConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.dto.AlarmInfoQueryDTO;
import com.allcore.external.dto.AlarmInfoUpdateDTO;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.entity.PositionDevice;
import com.allcore.external.mapper.AlarmInfoMapper;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.vo.AlarmInfoVO;
import com.allcore.external.vo.PositionWithPeopleVO;
import com.allcore.external.wrapper.AlarmInfoWrapper;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.fences.vo.FenceAlarmVO;
import com.allcore.main.code.inspection.dto.InspectionTaskPicQueryDTO;
import com.allcore.main.code.inspection.feign.InspectionPictureTaggingClient;
import com.allcore.main.code.inspection.feign.MainForAppClient;
import com.allcore.main.code.inspection.vo.DefectInfoOnlineReportingVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.allcore.common.constant.BasicConstant.*;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 09 5月 2025
 */
@Service
@AllArgsConstructor
public class AlarmInfoServiceImpl extends ServiceImpl<AlarmInfoMapper, AlarmInfo> implements IAlarmInfoService {

    //final IMainSourceClient IMainSourceClient;

    private final MainForAppClient mainClient;

    private final InspectionPictureTaggingClient inspectionPictureTaggingClient;

    @Override
    public IPage<AlarmInfoVO> getPageList(AlarmInfoQueryDTO dto, Query query) {
        if (StringUtils.isEmpty(dto.getTenantId())) {
            dto.setTenantId(AuthUtil.getTenantId());
        }
        // 分页查询
        IPage<AlarmInfo> paged = page(Condition.getPage(query), new LambdaQueryWrapper<AlarmInfo>()
                .likeRight(StringUtils.isNotBlank(dto.getDeptId()), AlarmInfo::getDeptId, dto.getDeptId())
                .eq(StringUtils.isNotBlank(dto.getDeviceType()), AlarmInfo::getDeviceType, dto.getDeviceType())
                .eq(StringUtils.isNotBlank(dto.getAlarmSource()), AlarmInfo::getAlarmSource, dto.getAlarmSource())
                .eq(StringUtils.isNotBlank(dto.getAlarmLevel()), AlarmInfo::getAlarmLevel, dto.getAlarmLevel())
                .eq(StringUtils.isNotBlank(dto.getAlarmStatus()), AlarmInfo::getAlarmStatus, dto.getAlarmStatus())
                .like(StringUtils.isNotBlank(dto.getAlarmContent()), AlarmInfo::getAlarmContent, dto.getAlarmContent())
                .between(Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()), AlarmInfo::getAlarmTime, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(AlarmInfo::getAlarmTime)
        );

        return paged.convert(alarmInfo -> {
            //字典转换
            AlarmInfoVO alarmInfoVO = AlarmInfoWrapper.build().entityVO(alarmInfo);
            enrichAlarmInfoVO(alarmInfoVO);
            return alarmInfoVO;
        });
    }

    @Override
    public AlarmInfoVO getDetail(String id) {
        AlarmInfo alarmInfo = this.getById(id);
        AlarmInfoVO alarmInfoVO = AlarmInfoWrapper.build().entityVO(alarmInfo);
        enrichAlarmInfoVO(alarmInfoVO);
        return alarmInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(List<AlarmInfoUpdateDTO> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return true;
        }
        List<AlarmInfo> alarmInfoList = dtoList.stream().map(AlarmInfoWrapper.build()::dtoVO).collect(Collectors.toList());
        return this.updateBatchById(alarmInfoList);
    }

    @Override
    public AlarmInfo getOneByInspectionTaskId(String inspectionTaskId) {
        return this.getOne(new LambdaQueryWrapper<AlarmInfo>().eq(AlarmInfo::getInspectionTaskId, inspectionTaskId));
    }

    @Override
    public AlarmInfo getAlarmInfoByTaggingId(String inspectionPictureTaggingId) {
        return this.getOne(new LambdaQueryWrapper<AlarmInfo>().eq(AlarmInfo::getInspectionPictureTaggingId, inspectionPictureTaggingId));

    }

    @Override
    public List<AlarmInfoVO> getList(AlarmInfoQueryDTO dto) {
        // 默认获取当天告警信息
        if (dto.getStartTime() == null && dto.getEndTime() == null) {
            dto.setStartTime(LocalDate.now().atStartOfDay());
            dto.setEndTime(LocalDateTime.now());
        }
        return list(new LambdaQueryWrapper<AlarmInfo>()
                .likeRight(StringUtils.isNotBlank(dto.getDeptId()), AlarmInfo::getDeptId, dto.getDeptId())
                .eq(StringUtils.isNotBlank(dto.getDeviceType()), AlarmInfo::getDeviceType, dto.getDeviceType())
                .eq(StringUtils.isNotBlank(dto.getAlarmSource()), AlarmInfo::getAlarmSource, dto.getAlarmSource())
                .eq(StringUtils.isNotBlank(dto.getAlarmLevel()), AlarmInfo::getAlarmLevel, dto.getAlarmLevel())
                .eq(StringUtils.isNotBlank(dto.getAlarmStatus()), AlarmInfo::getAlarmStatus, dto.getAlarmStatus())
                .like(StringUtils.isNotBlank(dto.getAlarmContent()), AlarmInfo::getAlarmContent, dto.getAlarmContent())
                .between(Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()), AlarmInfo::getAlarmTime, dto.getStartTime(), dto.getEndTime())
                .orderByDesc(AlarmInfo::getAlarmTime)
        ).stream().map(alarmInfo -> {
            //字典转换
            AlarmInfoVO alarmInfoVO = AlarmInfoWrapper.build().entityVO(alarmInfo);
            enrichAlarmInfoVO(alarmInfoVO);
            return alarmInfoVO;
        }).collect(Collectors.toList());
    }

    private void enrichAlarmInfoVO(AlarmInfoVO alarmInfoVO) {
        if (alarmInfoVO.getDeptId() != null) {
            Dept deptByDeptCode = SysCache.getDeptByDeptCode(alarmInfoVO.getDeptId());
            if (deptByDeptCode != null) {
                alarmInfoVO.setDeptName(deptByDeptCode.getDeptName());
            }
        }

        // 只有来源为缺陷识别，才可以用台账名称
        if (StringUtils.equals(alarmInfoVO.getAlarmSource(), BizDictEnum.ALARM_SOURCE_DEFECT_IDENTIFICATION.getCode())) {

            InspectionTaskPicQueryDTO dto= new InspectionTaskPicQueryDTO();
            dto.setInspectionPictureTaggingId(alarmInfoVO.getInspectionPictureTaggingId());
            R<List<DefectInfoOnlineReportingVO>> defectDetailInfoR = inspectionPictureTaggingClient.getDefectDetailInfo(dto);
            if (defectDetailInfoR.isSuccess()) {
                List<DefectInfoOnlineReportingVO> list = defectDetailInfoR.getData();
                if(PV.equals(alarmInfoVO.getDeviceType())){
                    alarmInfoVO.setDeviceName(list.get(0).getPvComponentName());
                }else{
                    alarmInfoVO.setDeviceName(list.get(0).getDeviceName());
                }
            }
        }

        if (alarmInfoVO.getDeviceId() != null) {
//            Map<String, String> map = new HashMap<>();
//            map.put(alarmInfoVO.getDeviceType(), alarmInfoVO.getDeviceId());
//            R<DeviceForMainNameDTO> deviceNameById = IMainSourceClient.getDeviceNameById(map);
//            R<Map<String, BasicCommonVO>> deviceNameById;
//            String deviceType = alarmInfoVO.getDeviceType();
//            if (StringUtil.equals(deviceType, TMS_LINE)) {
//                deviceNameById = mainClient.getDeviceByDeviceIds(CollectionUtil.newArrayList(alarmInfoVO.getDeviceId()), TMS_TOWER);
//            } else {
//                deviceNameById = mainClient.getDeviceByDeviceIds(CollectionUtil.newArrayList(alarmInfoVO.getDeviceId()), deviceType);
//            }
//            if (deviceNameById.isSuccess() && deviceNameById.getData() != null) {
//                alarmInfoVO.setDeviceName(deviceNameById.getData().get(alarmInfoVO.getDeviceId()).getDeviceName());
//            }
        }
        //如果是电子围栏的话，设备名称是通道
        //如果是集电线路，deviceid是设备名称
        //如果是轨道，deviceid是设备名称
        if (StringUtils.equals("integrated_line", alarmInfoVO.getAlarmSource())
                || StringUtils.equals("oribital_robot", alarmInfoVO.getAlarmSource())) {
            alarmInfoVO.setDeviceName(alarmInfoVO.getDeviceId());
        }
    }

    @Async
    @Override
    public void fenceSaveAlarmInfo(PositionWithPeopleVO vo) {
        R<FenceAlarmVO> result = mainClient.fenceAlarmVerification(vo.getPeopleId(), Double.toString(vo.getPositionX()), Double.toString(vo.getPositionY()));
        if (result.isSuccess()) {
            FenceAlarmVO data = result.getData();
            if (!data.isFenceAlarmStatus()) {
                return;
            }
            // 新增告警
            AlarmInfo alarmInfo = AlarmInfo.builder()
                    .deptId(vo.getDeptCode())
                    .deviceId(vo.getId())
                    .deviceName(vo.getName())
                    .deviceType(PB)
                    .alarmSource("electronic_fence") //电子围栏
                    .alarmType("personnel_entry") // 人员进入
                    .alarmContent("人员进入围栏")
                    .alarmLevel("normal")
                    .alarmStatus(0) //未确认
                    .alarmTime(LocalDateTime.now())
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            this.save(alarmInfo);
        }
    }

    @Override
    public boolean dealWith(String alarmId) {
        return update(new LambdaUpdateWrapper<AlarmInfo>()
                .set(AlarmInfo::getAlarmStatus, 3)
                .eq(AlarmInfo::getId, alarmId)
                .ne(AlarmInfo::getAlarmSource, BizDictEnum.ALARM_SOURCE_DEFECT_IDENTIFICATION.getCode())
        );
    }
}
