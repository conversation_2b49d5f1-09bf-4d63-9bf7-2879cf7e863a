package com.allcore.external.controller;

import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.external.dto.AlarmInfoQueryDTO;
import com.allcore.external.dto.AlarmInfoUpdateDTO;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.vo.AlarmInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>告警信息控制器</p>
 *
 * @author: sunkun
 * Date: 09 5月 2025
 */
@RestController
@RequestMapping("/alarm/info")
@RequiredArgsConstructor
@Api(tags = "告警信息控制器", value = "告警信息控制器")
public class AlarmInfoController {

    private final IAlarmInfoService alarmInfoService;

    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "")
    public R<IPage<AlarmInfoVO>> page(AlarmInfoQueryDTO dto, Query query) {
        IPage<AlarmInfoVO> page = alarmInfoService.getPageList(dto, query);
        return R.data(page);
    }

    @GetMapping("/detail")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "详情", notes = "")
    public R<AlarmInfoVO> detail(@ApiParam(value = "告警id", required = true) @RequestParam("alarmId") String alarmId) {
        AlarmInfoVO alarmInfoVO = alarmInfoService.getDetail(alarmId);
        return R.data(alarmInfoVO);
    }

    @GetMapping("/dealWith")
    @ApiOperation(value = "告警处理", notes = "告警处理 - 查看详情后调用 - 缺陷识别的不调用")
    public R dealWith(@ApiParam(value = "告警id", required = true) @RequestParam("alarmId") String alarmId) {
        return R.status(alarmInfoService.dealWith(alarmId));
    }

    @PostMapping("/update")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "修改", notes = "")
    public R update(@Valid @RequestBody List<AlarmInfoUpdateDTO> dtoList) {
        return R.status(alarmInfoService.update(dtoList));
    }

    @GetMapping("list")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "告警列表", notes = "告警列表")
    public R<List<AlarmInfoVO>> list(AlarmInfoQueryDTO dto) {
        List<AlarmInfoVO> list = alarmInfoService.getList(dto);
        return R.data(list);
    }
    //todo 计入白名单做不做

    //todo 无人机复检是什么


}
